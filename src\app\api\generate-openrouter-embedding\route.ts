import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { APP_CONFIG } from '@/lib/config';

interface CohereEmbeddingResponse {
  embeddings: number[][];
  texts: string[];
  meta: {
    api_version: {
      version: string;
    };
    billed_units: {
      input_tokens: number;
    };
  };
}

interface CohereErrorResponse {
  message: string;
}

export async function POST(request: NextRequest) {
  try {
    // Check for internal API token for server-to-server calls
    const authHeader = request.headers.get('authorization');
    const isInternalCall = authHeader === `Bearer ${process.env.INTERNAL_API_TOKEN || 'internal'}`;

    // Authenticate user (skip for internal calls)
    let user = null;
    if (!isInternalCall) {
      user = await getCurrentUser();
    }
    
    const body = await request.json();
    const { question, answer, query } = body;

    // Validate input - either question or query must be provided
    if (!question && !query) {
      return NextResponse.json(
        { error: 'Either question or query is required' },
        { status: 400 }
      );
    }

    // Check if OpenRouter is configured
    if (!process.env.OPENROUTER_API_KEY) {
      return NextResponse.json(
        { 
          error: 'OpenRouter API key not configured',
          fallback_available: true 
        },
        { status: 503 }
      );
    }

    console.log('🌐 Generating OpenRouter embedding server-side...');

    // Prepare text based on input type with enhanced multilingual support
    let text: string;
    let type: string;

    if (question) {
      // Q&A embedding with semantic enhancement
      const enhancedQuestion = enhanceQuestionText(question);
      const enhancedAnswer = answer ? enhanceAnswerText(answer) : '';
      text = enhancedAnswer
        ? `Question: ${enhancedQuestion}\nAnswer: ${enhancedAnswer}`
        : `Query: ${enhancedQuestion}`;
      type = answer ? 'qa-pair' : 'question-only';
    } else {
      // Search query embedding with multilingual synonyms
      const enhancedQuery = enhanceSearchQuery(query);
      text = `Query: ${enhancedQuery}`;
      type = 'search-query';
    }

    const startTime = Date.now();

    // Call Cohere API for embeddings using centralized config
    const embeddingConfig = APP_CONFIG.api.embedding;
    const response = await fetch(embeddingConfig.endpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${embeddingConfig.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: embeddingConfig.model,
        texts: [text],
        input_type: 'search_document',
        embedding_types: ['float']
      }),
    });

    const responseTime = Date.now() - startTime;

    if (!response.ok) {
      const errorData: CohereErrorResponse = await response.json();

      console.error('❌ Cohere API error:', {
        status: response.status,
        error: errorData.message,
        user_id: user?.id || 'internal'
      });

      // Handle specific error types
      let errorMessage = errorData.message;
      if (response.status === 429) {
        errorMessage = `Rate limited: ${errorData.message}`;
      } else if (response.status === 401) {
        errorMessage = 'Invalid Cohere API key';
      } else if (response.status === 402) {
        errorMessage = 'Insufficient credits on Cohere account';
      }

      return NextResponse.json(
        {
          error: errorMessage,
          status_code: response.status,
          fallback_available: true,
          type: 'cohere_api_error'
        },
        { status: response.status }
      );
    }

    const result: CohereEmbeddingResponse = await response.json();
    
    if (!result.embeddings || result.embeddings.length === 0) {
      return NextResponse.json(
        {
          error: 'No embedding data received from Cohere',
          fallback_available: true
        },
        { status: 500 }
      );
    }

    const embedding = result.embeddings[0];

    if (!embedding || embedding.length === 0) {
      return NextResponse.json(
        {
          error: 'Empty embedding received from Cohere',
          fallback_available: true
        },
        { status: 500 }
      );
    }

    // Validate embedding values
    if (embedding.some(val => !isFinite(val))) {
      return NextResponse.json(
        {
          error: 'Invalid embedding values received from Cohere',
          fallback_available: true
        },
        { status: 500 }
      );
    }

    console.log('✅ Cohere embedding generated successfully:', {
      model: embeddingConfig.model,
      dimensions: embedding.length,
      response_time_ms: responseTime,
      tokens_used: result.meta?.billed_units?.input_tokens || 0,
      text_length: text.length,
      type,
      user_id: user?.id || 'internal'
    });

    return NextResponse.json({
      success: true,
      embedding,
      dimensions: embedding.length,
      method: 'cohere-api',
      model: embeddingConfig.model,
      text_length: text.length,
      response_time_ms: responseTime,
      tokens_used: result.meta?.billed_units?.input_tokens || 0,
      type,
      features: embeddingConfig.features
    });

  } catch (error) {
    console.error('❌ Cohere embedding generation failed:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to generate Cohere embedding',
        details: error instanceof Error ? error.message : 'Unknown error',
        method: 'cohere-api',
        fallback_available: true
      },
      { status: 500 }
    );
  }
}

/**
 * Enhance search queries with multilingual synonyms and semantic variations
 */
function enhanceSearchQuery(query: string): string {
  const lowerQuery = query.toLowerCase().trim();

  // Define multilingual synonym mappings
  const synonymMappings: Record<string, string[]> = {
    // Price-related terms
    'price': ['cost', 'rate', 'amount', 'fee', 'charge', 'value', 'worth'],
    'kati': ['how much', 'price', 'cost', 'amount', 'kitna', 'कति'],
    'ho': ['is', 'hai', 'छ', 'cha'],
    'kitna': ['how much', 'kati', 'price', 'cost'],
    'paisa': ['money', 'price', 'cost', 'rupees', 'पैसा'],
    'dam': ['price', 'cost', 'rate', 'दाम'],

    // Quality-related terms
    'quality': ['material', 'fabric', 'texture', 'गुणस्तर', 'gunasthara'],
    'material': ['fabric', 'cloth', 'quality', 'सामग्री'],
    'kasto': ['how', 'what kind', 'कस्तो'],

    // Size-related terms
    'size': ['measurement', 'dimension', 'साइज'],
    'kati thulo': ['how big', 'size', 'dimension'],

    // Color-related terms
    'color': ['colour', 'rang', 'रंग'],
    'ke rang': ['what color', 'which color', 'कुन रंग'],

    // General question words
    'what': ['ke', 'कुन', 'कस्तो'],
    'how': ['kasto', 'kasari', 'कसरी', 'कस्तो'],
    'where': ['kaha', 'कहाँ'],
    'when': ['kaha', 'कहिले'],
    'why': ['kina', 'किन']
  };

  // Start with the original query
  let enhancedTerms = [lowerQuery];

  // Add synonyms for each word in the query
  const words = lowerQuery.split(/\s+/);
  for (const word of words) {
    if (synonymMappings[word]) {
      enhancedTerms.push(...synonymMappings[word]);
    }

    // Handle common Nepali/Hindi phrases
    if (word === 'kati' && words.includes('ho')) {
      enhancedTerms.push('how much', 'price', 'cost', 'amount');
    }
    if (word === 'kitna' && words.includes('hai')) {
      enhancedTerms.push('how much', 'price', 'cost', 'amount');
    }
  }

  // Handle common phrases
  if (lowerQuery.includes('kati ho') || lowerQuery.includes('कति छ')) {
    enhancedTerms.push('how much', 'price', 'cost', 'amount', 'rate');
  }
  if (lowerQuery.includes('kitna hai') || lowerQuery.includes('कितना है')) {
    enhancedTerms.push('how much', 'price', 'cost', 'amount', 'rate');
  }
  if (lowerQuery.includes('how much')) {
    enhancedTerms.push('price', 'cost', 'kati ho', 'kitna hai');
  }

  // Remove duplicates and join
  const uniqueTerms = [...new Set(enhancedTerms)];
  return uniqueTerms.join(' ');
}

/**
 * Enhance question text with semantic keywords
 */
function enhanceQuestionText(question: string): string {
  const lowerQuestion = question.toLowerCase();
  let enhanced = question;

  // Add semantic context based on question content
  if (lowerQuestion.includes('price') || lowerQuestion.includes('cost')) {
    enhanced += ' amount money rate fee charge value worth kati ho kitna hai';
  }
  if (lowerQuestion.includes('quality') || lowerQuestion.includes('material')) {
    enhanced += ' fabric texture cloth gunasthara';
  }
  if (lowerQuestion.includes('size') || lowerQuestion.includes('dimension')) {
    enhanced += ' measurement big small kati thulo';
  }
  if (lowerQuestion.includes('color') || lowerQuestion.includes('colour')) {
    enhanced += ' rang shade hue ke rang';
  }

  return enhanced;
}

/**
 * Enhance answer text with semantic keywords
 */
function enhanceAnswerText(answer: string): string {
  const lowerAnswer = answer.toLowerCase();
  let enhanced = answer;

  // Add semantic context based on answer content
  if (/\d+/.test(answer) && (lowerAnswer.includes('rs') || lowerAnswer.includes('rupee') || lowerAnswer.includes('₹'))) {
    enhanced += ' price cost amount money rate fee charge value worth';
  }
  if (lowerAnswer.includes('cotton') || lowerAnswer.includes('nylon') || lowerAnswer.includes('silk')) {
    enhanced += ' material fabric quality texture cloth';
  }
  if (lowerAnswer.includes('small') || lowerAnswer.includes('medium') || lowerAnswer.includes('large') || lowerAnswer.includes('xl')) {
    enhanced += ' size measurement dimension';
  }
  if (lowerAnswer.includes('red') || lowerAnswer.includes('blue') || lowerAnswer.includes('green') || lowerAnswer.includes('black') || lowerAnswer.includes('white')) {
    enhanced += ' color colour rang shade';
  }

  return enhanced;
}

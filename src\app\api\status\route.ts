import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { APP_CONFIG } from '@/lib/config';

interface StatusCheck {
  name: string;
  status: 'healthy' | 'warning' | 'error';
  message: string;
  details?: any;
  response_time_ms?: number;
}

interface TestResult {
  test_name: string;
  status: 'passed' | 'failed' | 'warning';
  message: string;
  details?: any;
  timestamp: string;
}

export async function GET(request: NextRequest) {
  try {
    const startTime = Date.now();
    const checks: StatusCheck[] = [];
    const testResults: TestResult[] = [];

    // 1. Database Connection Test
    const dbCheck = await checkDatabaseConnection();
    checks.push(dbCheck);

    // 2. Qdrant Connection Test
    const qdrantCheck = await checkQdrantConnection();
    checks.push(qdrantCheck);

    // 3. Gemini API Test
    const geminiCheck = await checkGeminiAPI();
    checks.push(geminiCheck);

    // 4. OpenRouter API Test (if configured)
    if (process.env.OPENROUTER_API_KEY) {
      const openrouterCheck = await checkOpenRouterAPI();
      checks.push(openrouterCheck);
    }

    // 5. Run automated tests
    const embeddingTest = await runEmbeddingTest();
    testResults.push(embeddingTest);

    const multilingualTest = await runMultilingualTest();
    testResults.push(multilingualTest);

    const n8nTest = await runN8nTest();
    testResults.push(n8nTest);

    const ragChatTest = await runRagChatTest();
    testResults.push(ragChatTest);

    const qaSyncTest = await runQaSyncTest();
    testResults.push(qaSyncTest);

    // Calculate overall health
    const healthyCount = checks.filter(check => check.status === 'healthy').length;
    const overallHealth = healthyCount === checks.length ? 'healthy' : 
                         healthyCount > 0 ? 'warning' : 'error';

    // Calculate test success rate
    const passedTests = testResults.filter(test => test.status === 'passed').length;
    const testSuccessRate = testResults.length > 0 ? (passedTests / testResults.length) * 100 : 0;

    // Get system statistics
    const stats = await getSystemStats();

    const totalTime = Date.now() - startTime;

    return NextResponse.json({
      success: true,
      overall_health: overallHealth,
      message: `System is ${overallHealth}. ${healthyCount}/${checks.length} services operational.`,
      services: checks,
      test_results: {
        summary: {
          total_tests: testResults.length,
          passed: passedTests,
          failed: testResults.filter(test => test.status === 'failed').length,
          warnings: testResults.filter(test => test.status === 'warning').length,
          success_rate: Math.round(testSuccessRate),
        },
        tests: testResults
      },
      system_stats: stats,
      performance: {
        status_check_time_ms: totalTime,
        timestamp: new Date().toISOString()
      },
      endpoints: {
        'GET /api/status': 'This endpoint - system status and health checks',
        'GET /api/test': 'Basic database and table structure tests',
        'GET /api/test-rag-chat': 'Complete RAG workflow test',
        'GET /api/test-multilingual-embedding': 'Multilingual embedding test',
        'GET /api/test-embedding-status': 'Embedding generation test',
        'GET /api/test-qa-sync': 'Q&A synchronization workflow test',
        'GET /api/test-complete-rag-workflow': 'End-to-end RAG workflow test',
        'GET /api/n8n/status': 'n8n integration status (requires API key)',
        'GET /api/n8n/docs': 'n8n API documentation',
        'GET /api/n8n/{collection_id}': 'n8n collection info and testing'
      },
      supported_languages: APP_CONFIG.rag.supportedLanguages
    });

  } catch (error) {
    console.error('❌ Status check failed:', error);
    
    return NextResponse.json(
      { 
        success: false,
        overall_health: 'error',
        error: 'Failed to perform status check',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

async function checkDatabaseConnection(): Promise<StatusCheck> {
  const startTime = Date.now();
  try {
    const { data, error } = await supabase
      .from('collections')
      .select('count', { count: 'exact', head: true });

    const responseTime = Date.now() - startTime;

    if (error) {
      return {
        name: 'Supabase Database',
        status: 'error',
        message: `Connection failed: ${error.message}`,
        response_time_ms: responseTime
      };
    }

    return {
      name: 'Supabase Database',
      status: 'healthy',
      message: `Connected successfully. Collections: ${data || 0}`,
      response_time_ms: responseTime,
      details: {
        collections_count: data || 0,
        url: process.env.NEXT_PUBLIC_SUPABASE_URL
      }
    };
  } catch (error) {
    return {
      name: 'Supabase Database',
      status: 'error',
      message: `Connection error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      response_time_ms: Date.now() - startTime
    };
  }
}

async function checkQdrantConnection(): Promise<StatusCheck> {
  const startTime = Date.now();
  
  if (!process.env.QDRANT_URL) {
    return {
      name: 'Qdrant Vector Database',
      status: 'warning',
      message: 'QDRANT_URL not configured - vector search disabled',
      response_time_ms: Date.now() - startTime,
      details: {
        note: 'App works without vector search but with limited functionality'
      }
    };
  }

  try {
    // Use absolute URL for server-side fetch
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/test-qdrant`);
    const result = await response.json();
    const responseTime = Date.now() - startTime;

    if (!response.ok || !result.success) {
      return {
        name: 'Qdrant Vector Database',
        status: 'warning',
        message: result.error || 'Connection failed',
        response_time_ms: responseTime,
        details: result.details
      };
    }

    return {
      name: 'Qdrant Vector Database',
      status: 'healthy',
      message: `Connected successfully. Collections: ${result.collections?.length || 0}`,
      response_time_ms: responseTime,
      details: {
        collections: result.collections,
        url: result.qdrantUrl
      }
    };
  } catch (error) {
    return {
      name: 'Qdrant Vector Database',
      status: 'warning',
      message: 'Connection test failed',
      response_time_ms: Date.now() - startTime,
      details: {
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    };
  }
}

async function checkGeminiAPI(): Promise<StatusCheck> {
  const startTime = Date.now();
  
  if (!process.env.GEMINI_API_KEY) {
    return {
      name: 'Gemini AI API',
      status: 'error',
      message: 'GEMINI_API_KEY not configured',
      response_time_ms: Date.now() - startTime,
      details: {
        required_for: ['RAG chat', 'embedding generation']
      }
    };
  }

  try {
    const { GoogleGenerativeAI } = await import('@google/generative-ai');
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    const model = genAI.getGenerativeModel({ model: APP_CONFIG.api.chat.model });

    const result = await model.generateContent('Hello');
    const response = result.response;
    const responseTime = Date.now() - startTime;

    if (response.text()) {
      return {
        name: 'Gemini AI API',
        status: 'healthy',
        message: 'API key working correctly',
        response_time_ms: responseTime,
        details: {
          model: APP_CONFIG.api.chat.model,
          embedding_model: APP_CONFIG.api.embedding.model
        }
      };
    } else {
      return {
        name: 'Gemini AI API',
        status: 'warning',
        message: 'API key configured but response empty',
        response_time_ms: responseTime
      };
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    // Handle quota exceeded gracefully
    if (errorMessage.includes('429') || errorMessage.includes('quota') || errorMessage.includes('Too Many Requests')) {
      return {
        name: 'Gemini AI API',
        status: 'warning',
        message: 'API quota exceeded - service temporarily limited',
        response_time_ms: Date.now() - startTime,
        details: {
          error: 'Quota limit reached. Embedding generation may still work.',
          note: 'This is expected with free tier usage'
        }
      };
    }

    return {
      name: 'Gemini AI API',
      status: 'error',
      message: 'API key test failed',
      response_time_ms: Date.now() - startTime,
      details: {
        error: errorMessage
      }
    };
  }
}

async function checkOpenRouterAPI(): Promise<StatusCheck> {
  const startTime = Date.now();
  
  try {
    // Use absolute URL for server-side fetch
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/test-multilingual-embedding`, {
      method: 'GET'
    });
    const result = await response.json();
    const responseTime = Date.now() - startTime;

    if (response.ok && result.results?.openrouter_available) {
      return {
        name: 'OpenRouter API',
        status: 'healthy',
        message: 'Multilingual embedding service available',
        response_time_ms: responseTime,
        details: {
          model: 'cohere/embed-multilingual-v3.0',
          features: ['Multilingual support', 'High-quality embeddings']
        }
      };
    } else {
      return {
        name: 'OpenRouter API',
        status: 'warning',
        message: 'Service not available or configured',
        response_time_ms: responseTime
      };
    }
  } catch (error) {
    return {
      name: 'OpenRouter API',
      status: 'warning',
      message: 'Connection test failed',
      response_time_ms: Date.now() - startTime,
      details: {
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    };
  }
}

async function runEmbeddingTest(): Promise<TestResult> {
  try {
    // Use absolute URL for server-side fetch
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/test-embedding-status`, {
      method: 'GET'
    });

    const result = await response.json();

    if (response.ok && result.success) {
      return {
        test_name: 'Embedding Generation',
        status: 'passed',
        message: `Successfully generated ${result.dimensions}D embedding`,
        details: {
          model: result.model,
          dimensions: result.dimensions,
          response_time_ms: result.response_time_ms
        },
        timestamp: new Date().toISOString()
      };
    } else {
      return {
        test_name: 'Embedding Generation',
        status: 'failed',
        message: result.error || 'Embedding generation failed',
        details: result,
        timestamp: new Date().toISOString()
      };
    }
  } catch (error) {
    return {
      test_name: 'Embedding Generation',
      status: 'failed',
      message: 'Test execution failed',
      details: { error: error instanceof Error ? error.message : 'Unknown error' },
      timestamp: new Date().toISOString()
    };
  }
}

async function runMultilingualTest(): Promise<TestResult> {
  try {
    // Use absolute URL for server-side fetch
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/test-multilingual-embedding`, {
      method: 'GET'
    });

    const result = await response.json();

    if (response.ok && result.success) {
      const passedTests = result.test_results?.filter((t: any) => t.success).length || 0;
      const totalTests = result.test_results?.length || 0;
      
      return {
        test_name: 'Multilingual Search',
        status: passedTests === totalTests ? 'passed' : 'warning',
        message: `${passedTests}/${totalTests} multilingual tests passed`,
        details: {
          test_results: result.test_results,
          enhancement_examples: result.enhancement_examples
        },
        timestamp: new Date().toISOString()
      };
    } else {
      return {
        test_name: 'Multilingual Search',
        status: 'failed',
        message: result.error || 'Multilingual test failed',
        details: result,
        timestamp: new Date().toISOString()
      };
    }
  } catch (error) {
    return {
      test_name: 'Multilingual Search',
      status: 'failed',
      message: 'Test execution failed',
      details: { error: error instanceof Error ? error.message : 'Unknown error' },
      timestamp: new Date().toISOString()
    };
  }
}

async function runN8nTest(): Promise<TestResult> {
  try {
    const response = await fetch('/api/n8n/status', {
      headers: {
        'x-api-key': process.env.N8N_API_KEY || 'your-secret-api-key'
      }
    });

    const result = await response.json();

    if (response.ok && result.success) {
      return {
        test_name: 'n8n Integration',
        status: result.status === 'healthy' ? 'passed' : 'warning',
        message: `n8n endpoints ${result.status}`,
        details: {
          services: result.services,
          endpoints: result.endpoints
        },
        timestamp: new Date().toISOString()
      };
    } else {
      return {
        test_name: 'n8n Integration',
        status: 'warning',
        message: result.error || 'n8n test failed',
        details: result,
        timestamp: new Date().toISOString()
      };
    }
  } catch (error) {
    return {
      test_name: 'n8n Integration',
      status: 'warning',
      message: 'Test execution failed',
      details: { error: error instanceof Error ? error.message : 'Unknown error' },
      timestamp: new Date().toISOString()
    };
  }
}

async function runRagChatTest(): Promise<TestResult> {
  try {
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/test-rag-chat`, {
      method: 'GET'
    });

    const result = await response.json();

    if (response.ok && result.success) {
      return {
        test_name: 'RAG Chat Functionality',
        status: 'passed',
        message: 'RAG chat workflow completed successfully',
        details: {
          workflow: result.results?.workflow,
          test_qa_pairs: result.results?.testQAPairs,
          search_results: result.results?.searchResults
        },
        timestamp: new Date().toISOString()
      };
    } else {
      return {
        test_name: 'RAG Chat Functionality',
        status: 'failed',
        message: result.message || 'RAG chat test failed',
        details: result,
        timestamp: new Date().toISOString()
      };
    }
  } catch (error) {
    return {
      test_name: 'RAG Chat Functionality',
      status: 'failed',
      message: 'Test execution failed',
      details: { error: error instanceof Error ? error.message : 'Unknown error' },
      timestamp: new Date().toISOString()
    };
  }
}

async function runQaSyncTest(): Promise<TestResult> {
  try {
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/test-qa-sync`, {
      method: 'GET'
    });

    const result = await response.json();

    if (response.ok && result.success) {
      return {
        test_name: 'Q&A Sync Workflow',
        status: 'passed',
        message: 'Q&A sync workflow completed successfully',
        details: {
          workflow: result.results?.workflow,
          collection_name: result.results?.collectionName,
          vector_dimensions: result.results?.vectorDimensions
        },
        timestamp: new Date().toISOString()
      };
    } else {
      return {
        test_name: 'Q&A Sync Workflow',
        status: 'failed',
        message: result.message || 'Q&A sync test failed',
        details: result,
        timestamp: new Date().toISOString()
      };
    }
  } catch (error) {
    return {
      test_name: 'Q&A Sync Workflow',
      status: 'failed',
      message: 'Test execution failed',
      details: { error: error instanceof Error ? error.message : 'Unknown error' },
      timestamp: new Date().toISOString()
    };
  }
}

async function getSystemStats() {
  try {
    const [collectionsResult, qaPairsResult, usersResult] = await Promise.all([
      supabase.from('collections').select('count', { count: 'exact', head: true }),
      supabase.from('qa_pairs').select('count', { count: 'exact', head: true }),
      supabase.from('users').select('count', { count: 'exact', head: true })
    ]);

    return {
      total_collections: collectionsResult.count || 0,
      total_qa_pairs: qaPairsResult.count || 0,
      total_users: usersResult.count || 0,
      environment: process.env.NODE_ENV || 'development'
    };
  } catch (error) {
    return {
      total_collections: 0,
      total_qa_pairs: 0,
      total_users: 0,
      environment: process.env.NODE_ENV || 'development',
      error: 'Failed to fetch stats'
    };
  }
}

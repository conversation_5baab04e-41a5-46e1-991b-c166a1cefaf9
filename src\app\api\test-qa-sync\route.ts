import { NextResponse } from 'next/server';
import { QdrantClient } from '@qdrant/js-client-rest';

async function runQaSyncTest() {
  try {
    console.log('🔄 Testing full Q&A sync workflow...');

    // Check if Qdrant is configured
    if (!process.env.QDRANT_URL) {
      return NextResponse.json(
        { error: 'Qdrant is not configured' },
        { status: 503 }
      );
    }

    // Initialize Qdrant client
    const qdrantClient = new QdrantClient({
      url: process.env.QDRANT_URL,
      apiKey: process.env.QDRANT_API_KEY,
    });

    // Simulate the embedding generation (normally done with Gemini)
    const testVector = Array.from({ length: 768 }, () => Math.random() * 2 - 1);
    
    // Test data
    const testData = {
      question: "What is the capital of France?",
      answer: "The capital of France is Paris.",
      vector: testVector,
      collection_id: "test-collection-123",
      user_id: "test-user-456"
    };

    console.log('📊 Test Q&A data:', {
      question: testData.question,
      answer: testData.answer,
      vectorLength: testData.vector.length,
      collection_id: testData.collection_id,
      user_id: testData.user_id
    });

    // Create collection name for Qdrant
    const qdrantCollectionName = `user_${testData.user_id}_collection_${testData.collection_id}`;

    try {
      // Check if collection exists, create if it doesn't
      const collections = await qdrantClient.getCollections();
      const collectionExists = collections.collections?.some(
        (col) => col.name === qdrantCollectionName
      );

      if (!collectionExists) {
        console.log('📊 Creating Qdrant collection:', qdrantCollectionName);
        await qdrantClient.createCollection(qdrantCollectionName, {
          vectors: {
            size: testData.vector.length,
            distance: 'Cosine',
          },
        });
        console.log('✅ Collection created');
      } else {
        console.log('✅ Collection already exists');
      }

      // Generate point ID using the same logic as the main API
      let simpleHash = 0;
      const combinedText = `${testData.collection_id}_${testData.question}`;
      for (let i = 0; i < combinedText.length; i++) {
        simpleHash = ((simpleHash << 5) - simpleHash + combinedText.charCodeAt(i)) & 0x7fffffff;
      }
      const pointId = Math.abs(simpleHash);

      console.log('📊 Generated point ID:', pointId);

      // Upsert the vector point
      const upsertData = {
        wait: true,
        points: [
          {
            id: pointId,
            vector: testData.vector,
            payload: {
              question: testData.question,
              answer: testData.answer,
              collection_id: testData.collection_id,
              user_id: testData.user_id,
              created_at: new Date().toISOString(),
            },
          },
        ],
      };

      console.log('📊 Upserting vector...');
      await qdrantClient.upsert(qdrantCollectionName, upsertData);
      console.log('✅ Vector upserted successfully');

      // Test search functionality
      console.log('📊 Testing vector search...');
      const searchResult = await qdrantClient.search(qdrantCollectionName, {
        vector: testData.vector,
        limit: 5,
        score_threshold: 0.5,
        with_payload: true,
      });

      console.log('✅ Search completed, results:', searchResult.length);

      // Clean up - delete the test point
      console.log('📊 Cleaning up test data...');
      await qdrantClient.delete(qdrantCollectionName, {
        wait: true,
        points: [pointId],
      });
      console.log('✅ Test data cleaned up');

      return NextResponse.json({
        success: true,
        message: 'Full Q&A sync workflow test completed successfully',
        results: {
          collectionName: qdrantCollectionName,
          pointId: pointId,
          vectorDimensions: testData.vector.length,
          searchResults: searchResult.length,
          workflow: [
            'Collection created/verified',
            'Vector upserted',
            'Search tested',
            'Data cleaned up'
          ]
        },
      });

    } catch (qdrantError) {
      console.error('❌ Qdrant operation failed:', qdrantError);
      
      if (qdrantError && typeof qdrantError === 'object' && 'data' in qdrantError) {
        console.error('❌ Qdrant error data:', JSON.stringify(qdrantError.data, null, 2));
      }

      return NextResponse.json(
        { 
          error: 'Qdrant operation failed', 
          details: qdrantError instanceof Error ? qdrantError.message : 'Unknown error',
          qdrantErrorData: qdrantError && typeof qdrantError === 'object' && 'data' in qdrantError ? qdrantError.data : undefined
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
    return NextResponse.json(
      { 
        error: 'Test failed', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

// GET endpoint for browser testing
export async function GET() {
  return runQaSyncTest();
}

// POST endpoint for backward compatibility
export async function POST() {
  return runQaSyncTest();
}
